import { registerAs } from '@nestjs/config';
import { BullModuleOptions } from '@nestjs/bull';
import * as Jo<PERSON> from 'joi';

export interface QueueConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  maxRetriesPerRequest: number;
  retryDelayOnFailover: number;
  enableReadyCheck: boolean;
}

export const queueConfig = registerAs('queue', (): BullModuleOptions => {
  const config = {
    host: process.env.QUEUE_REDIS_HOST || process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.QUEUE_REDIS_PORT || process.env.REDIS_PORT || '6379', 10),
    password: process.env.QUEUE_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
    db: parseInt(process.env.QUEUE_REDIS_DB || '1', 10), // Different DB for queues
    maxRetriesPerRequest: parseInt(process.env.QUEUE_MAX_RETRIES || '3', 10),
    retryDelayOnFailover: parseInt(process.env.QUEUE_RETRY_DELAY || '100', 10),
    enableReadyCheck: process.env.QUEUE_READY_CHECK !== 'false',
  };

  // Validate configuration
  const schema = Joi.object({
    host: Joi.string().required(),
    port: Joi.number().port().required(),
    password: Joi.string().optional(),
    db: Joi.number().min(0).max(15).required(),
    maxRetriesPerRequest: Joi.number().min(0).required(),
    retryDelayOnFailover: Joi.number().min(0).required(),
    enableReadyCheck: Joi.boolean().required(),
  });

  const { error } = schema.validate(config);
  if (error) {
    throw new Error(`Queue configuration validation error: ${error.message}`);
  }

  return {
    redis: {
      host: config.host,
      port: config.port,
      password: config.password,
      db: config.db,
      // Remove these options for Bull compatibility
      // maxRetriesPerRequest: config.maxRetriesPerRequest,
      // enableReadyCheck: config.enableReadyCheck,
    },
    defaultJobOptions: {
      removeOnComplete: 100,
      removeOnFail: 50,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
    },
  };
});
